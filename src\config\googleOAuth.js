// Google OAuth Configuration for Houzer API
// Using custom OAuth URL provided by <PERSON><PERSON><PERSON> backend

export const GOOGLE_OAUTH_CONFIG = {
  // Houzer Google OAuth URL
  authUrl: process.env.REACT_APP_GOOGLE_AUTH_URL || "https://betaapi.houzer.co.in/houzer/oauth2/authorize/google?redirect_uri=https://beta.app.houzer.co.in/houzer/oauth2/redirect",

  // OAuth scopes - what information we want to access
  scope: "profile email",

  // Response type
  responseType: "code",

  // Access type
  accessType: "offline",

  // Prompt
  prompt: "consent"
};

// Environment setup instructions:
// Create a .env file in your project root and add:
// REACT_APP_GOOGLE_AUTH_URL=https://betaapi.houzer.co.in/houzer/oauth2/authorize/google?redirect_uri=https://beta.app.houzer.co.in/houzer/oauth2/redirect

export default GOOGLE_OAUTH_CONFIG;
