import { useState } from 'react';
import { Button, CircularProgress, Box, Typography } from '@mui/material';
import { Google as GoogleIcon } from '@mui/icons-material';
import googleAuthService from '../services/googleAuthService';

const GoogleOAuthButton = ({
  onSuccess,
  onError,
  disabled = false,
  variant = "outlined",
  size = "large",
  fullWidth = true,
  text = "Continue with Google"
}) => {
  const [loading, setLoading] = useState(false);

  const handleGoogleSignIn = async () => {
    setLoading(true);

    try {
      const result = await googleAuthService.signIn();

      if (result.success) {
        // If redirecting, we don't need to call onSuc<PERSON> as the page will redirect
        if (result.redirecting) {
          // Page will redirect, so we don't reset loading state
          return;
        }

        if (onSuccess) {
          onSuccess(result);
        }
      } else {
        if (onError) {
          onError(result.error || 'Google sign-in failed');
        }
      }
    } catch (error) {
      console.error('Google sign-in error:', error);
      if (onError) {
        onError('An unexpected error occurred during Google sign-in');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      fullWidth={fullWidth}
      disabled={disabled || loading}
      onClick={handleGoogleSignIn}
      startIcon={
        loading ? (
          <CircularProgress size={20} />
        ) : (
          <GoogleIcon />
        )
      }
      sx={{
        borderColor: '#dadce0',
        color: '#3c4043',
        backgroundColor: '#fff',
        textTransform: 'none',
        fontWeight: 500,
        fontSize: '14px',
        height: '48px',
        '&:hover': {
          backgroundColor: '#f8f9fa',
          borderColor: '#dadce0',
          boxShadow: '0 1px 2px 0 rgba(60,64,67,.30), 0 1px 3px 1px rgba(60,64,67,.15)',
        },
        '&:focus': {
          backgroundColor: '#f8f9fa',
          borderColor: '#4285f4',
          outline: 'none',
        },
        '&:disabled': {
          backgroundColor: '#f8f9fa',
          color: '#9aa0a6',
          borderColor: '#f8f9fa',
        },
        boxShadow: '0 1px 2px 0 rgba(60,64,67,.30), 0 1px 3px 1px rgba(60,64,67,.15)',
        border: '1px solid #dadce0',
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        {loading ? (
          <Typography variant="body2">Redirecting...</Typography>
        ) : (
          <Typography variant="body2">{text}</Typography>
        )}
      </Box>
    </Button>
  );
};

export default GoogleOAuthButton;
