import { useState } from 'react';
import { 
  FormControl, 
  TextField, 
  FormHelperText, 
  InputAdornment, 
  IconButton 
} from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import { Controller } from 'react-hook-form';

const PasswordField = ({ 
  name, 
  control, 
  rules, 
  label, 
  placeholder,
  error,
  helperText,
  size = 'small',
  fullWidth = true,
  margin = 'normal',
  ...props 
}) => {
  const [showPassword, setShowPassword] = useState(false);

  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };

  return (
    <FormControl fullWidth={fullWidth} margin={margin}>
      <Controller
        name={name}
        control={control}
        rules={rules}
        render={({ field: { value, onChange } }) => (
          <TextField
            value={value || ''}
            label={label}
            type={showPassword ? 'text' : 'password'}
            placeholder={placeholder}
            onChange={onChange}
            error={Bo<PERSON><PERSON>(error)}
            size={size}
            fullWidth={fullWidth}
            variant="outlined"
            aria-describedby={`${name}-helper-text`}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle password visibility"
                    onClick={handleClickShowPassword}
                    onMouseDown={handleMouseDownPassword}
                    edge="end"
                  >
                    {showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
            {...props}
          />
        )}
      />
      {error && (
        <FormHelperText sx={{ color: 'error.main' }} id={`${name}-helper-text`}>
          {helperText}
        </FormHelperText>
      )}
    </FormControl>
  );
};

export default PasswordField;
