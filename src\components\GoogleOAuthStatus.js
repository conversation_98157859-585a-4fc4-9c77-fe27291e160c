import { useState, useEffect } from 'react';
import { 
  Alert, 
  AlertTitle, 
  Box, 
  Typography, 
  Link,
  Chip
} from '@mui/material';
import { 
  CheckCircle as CheckIcon, 
  Warning as WarningIcon,
  Info as InfoIcon 
} from '@mui/icons-material';
import { GOOGLE_OAUTH_CONFIG } from '../config/googleOAuth';

const GoogleOAuthStatus = () => {
  const [status, setStatus] = useState('checking');
  const [message, setMessage] = useState('');

  useEffect(() => {
    checkGoogleOAuthSetup();
  }, []);

  const checkGoogleOAuthSetup = () => {
    const authUrl = GOOGLE_OAUTH_CONFIG.authUrl;

    if (!authUrl || authUrl.includes('betaapi.houzer.co.in')) {
      setStatus('configured');
      setMessage('Google OAuth is configured with Houzer API and ready to use.');
    } else {
      setStatus('not-configured');
      setMessage('Google OAuth URL is not properly configured.');
    }
  };

  const getAlertProps = () => {
    switch (status) {
      case 'configured':
        return {
          severity: 'success',
          icon: <CheckIcon />,
          title: 'Google OAuth Ready'
        };
      case 'not-configured':
        return {
          severity: 'warning',
          icon: <WarningIcon />,
          title: 'Setup Required'
        };
      default:
        return {
          severity: 'info',
          icon: <InfoIcon />,
          title: 'Checking Configuration'
        };
    }
  };

  const alertProps = getAlertProps();

  return (
    <Box sx={{ mb: 2 }}>
      <Alert 
        severity={alertProps.severity} 
        icon={alertProps.icon}
        sx={{ 
          borderRadius: 2,
          '& .MuiAlert-message': {
            width: '100%'
          }
        }}
      >
        <AlertTitle sx={{ fontWeight: 600 }}>
          {alertProps.title}
        </AlertTitle>
        
        <Typography variant="body2" sx={{ mb: 1 }}>
          {message}
        </Typography>

        {status === 'configured' && (
          <Box sx={{ mt: 1 }}>
            <Chip
              label="Houzer OAuth Ready"
              color="success"
              size="small"
              sx={{ mr: 1 }}
            />
            <Chip
              label="Responsive Design"
              color="primary"
              size="small"
              sx={{ mr: 1 }}
            />
            <Chip
              label="Mobile Optimized"
              color="secondary"
              size="small"
            />
          </Box>
        )}

        {status === 'not-configured' && (
          <Box sx={{ mt: 1 }}>
            <Typography variant="body2" sx={{ mb: 1 }}>
              To configure Google OAuth:
            </Typography>
            <Typography variant="body2" component="div">
              1. Update your <code>.env</code> file with the correct Google Auth URL
              <br />
              2. Ensure the URL points to your Houzer API endpoint
              <br />
              3. Restart the development server
            </Typography>
          </Box>
        )}
      </Alert>
    </Box>
  );
};

export default GoogleOAuthStatus;
