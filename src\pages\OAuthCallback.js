import {
  Alert,
  Box,
  CircularProgress,
  Container,
  Paper,
  Typography
} from '@mui/material';
import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import authService from '../api/authService';

const OAuthCallback = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    handleOAuthCallback();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleOAuthCallback = async () => {
    try {
      // Extract parameters from URL
      const code = searchParams.get('code');
      const state = searchParams.get('state');
      const error = searchParams.get('error');

      if (error) {
        setError(`OAuth error: ${error}`);
        setLoading(false);
        return;
      }

      if (!code) {
        setError('No authorization code received from OAuth provider');
        setLoading(false);
        return;
      }

      // Handle successful OAuth callback
      const handleSuccess = (data) => {
        console.log('OAuth callback successful:', data);
        // Redirect to profile or dashboard
        navigate('/profile');
      };

      const handleError = (errorMessage) => {
        setError(errorMessage);
        setLoading(false);
      };

      // Send the authorization code to your backend
      await authService.handleOAuthCallback({
        code,
        state
      }, handleError, handleSuccess);

    } catch (err) {
      console.error('OAuth callback error:', err);
      setError('Failed to process OAuth callback');
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Container 
        component="main" 
        maxWidth="xs"
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        <Paper 
          elevation={3} 
          sx={{ 
            p: 4, 
            textAlign: 'center',
            width: '100%'
          }}
        >
          <CircularProgress size={48} sx={{ mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            Completing sign in...
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Please wait while we process your authentication
          </Typography>
        </Paper>
      </Container>
    );
  }

  if (error) {
    return (
      <Container 
        component="main" 
        maxWidth="xs"
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        <Paper 
          elevation={3} 
          sx={{ 
            p: 4, 
            width: '100%'
          }}
        >
          <Alert severity="error" sx={{ mb: 2 }}>
            <Typography variant="h6" gutterBottom>
              Authentication Failed
            </Typography>
            <Typography variant="body2">
              {error}
            </Typography>
          </Alert>
          
          <Box sx={{ textAlign: 'center', mt: 3 }}>
            <Typography 
              variant="body2" 
              color="primary" 
              sx={{ 
                cursor: 'pointer',
                textDecoration: 'underline'
              }}
              onClick={() => navigate('/login')}
            >
              Return to Login
            </Typography>
          </Box>
        </Paper>
      </Container>
    );
  }

  return null;
};

export default OAuthCallback;
