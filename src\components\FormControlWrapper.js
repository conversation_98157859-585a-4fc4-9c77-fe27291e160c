import { FormControl, FormHelperText, TextField } from '@mui/material';
import { Controller } from 'react-hook-form';

const FormControlWrapper = ({ 
  name, 
  control, 
  rules, 
  label, 
  type = 'text', 
  placeholder,
  error,
  helperText,
  size = 'small',
  fullWidth = true,
  margin = 'normal',
  ...props 
}) => {
  return (
    <FormControl fullWidth={fullWidth} margin={margin}>
      <Controller
        name={name}
        control={control}
        rules={rules}
        render={({ field: { value, onChange } }) => (
          <TextField
            value={value || ''}
            label={label}
            type={type}
            placeholder={placeholder}
            onChange={onChange}
            error={Boolean(error)}
            size={size}
            fullWidth={fullWidth}
            variant="outlined"
            aria-describedby={`${name}-helper-text`}
            {...props}
          />
        )}
      />
      {error && (
        <FormHelperText sx={{ color: 'error.main' }} id={`${name}-helper-text`}>
          {helperText}
        </FormHelperText>
      )}
    </FormControl>
  );
};

export default FormControlWrapper;
