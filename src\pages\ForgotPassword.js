import { yupResolver } from '@hookform/resolvers/yup';
import {
  <PERSON><PERSON>,
  Box,
  Button,
  CircularProgress,
  Container,
  Grid,
  Link,
  Paper,
  Snackbar,
  Typography
} from '@mui/material';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Link as RouterLink } from 'react-router-dom';
import * as Yup from 'yup';
import authService from '../api/authService';
import FormControlWrapper from '../components/FormControlWrapper';

const validationSchema = Yup.object({
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required')
});

const defaultValues = {
  email: ''
};

const ForgotPassword = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showError, setShowError] = useState(false);
  const [success, setSuccess] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors }
  } = useForm({
    defaultValues,
    mode: 'onChange',
    resolver: yupResolver(validationSchema)
  });

  const onSubmit = async (values) => {
    setLoading(true);

    const handleSuccess = (data) => {
      console.log('Password reset request successful:', data);
      setSuccess(true);
    };

    const handleError = (errorMessage) => {
      setError(errorMessage);
      setShowError(true);
    };

    try {
      await authService.forgotPassword(values.email, handleError, handleSuccess);
    } catch (err) {
      // Error already handled by authService
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container component="main" maxWidth="xs">
      <Paper elevation={3} sx={{ p: 4, mt: 8 }}>
        <Typography component="h1" variant="h5" align="center" gutterBottom>
          Forgot Password
        </Typography>
        {!success ? (
          <form onSubmit={handleSubmit(onSubmit)}>
            <Typography variant="body2" color="textSecondary" align="center" sx={{ mb: 3 }}>
              Enter your email address and we'll send you a link to reset your password.
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <FormControlWrapper
                  name="email"
                  control={control}
                  rules={{ required: 'Email is required' }}
                  label="Email Address"
                  type="email"
                  placeholder="Enter your email"
                  error={errors.email}
                  helperText={errors.email?.message}
                />
              </Grid>
              <Grid item xs={12}>
                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  size="large"
                  sx={{ mt: 2, mb: 2 }}
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <CircularProgress
                        size={20}
                        sx={{ color: 'common.white', mr: 1 }}
                      />
                      Sending...
                    </>
                  ) : (
                    'Send Reset Link'
                  )}
                </Button>
              </Grid>
            </Grid>
            <Box sx={{ textAlign: 'center', mt: 2 }}>
              <Link component={RouterLink} to="/login" variant="body2">
                Back to Login
              </Link>
            </Box>
          </form>
        ) : (
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="body1" color="primary" gutterBottom>
              Password reset link has been sent to your email.
            </Typography>
            <Button
              component={RouterLink}
              to="/login"
              variant="contained"
              sx={{ mt: 2 }}
            >
              Back to Login
            </Button>
          </Box>
        )}
      </Paper>
      <Snackbar
        open={showError}
        autoHideDuration={6000}
        onClose={() => setShowError(false)}
      >
        <Alert severity="error" onClose={() => setShowError(false)}>
          {error}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default ForgotPassword; 