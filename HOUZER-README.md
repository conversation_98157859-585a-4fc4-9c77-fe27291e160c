# Houzer-Style Authentication System

## Overview

This repository contains a comprehensive authentication system following the Houzer application architecture pattern. It features OTP-based verification, Google signup integration, comprehensive audit logging, and a modular database design optimized for enterprise applications.

## 🏗️ Architecture Features

### Core Authentication Flow
- **OTP Verification**: Email and SMS-based OTP verification system
- **Google Integration**: Seamless Google OAuth signup and login
- **Multi-Organization Support**: Individual users can belong to multiple organizations
- **Comprehensive Audit Logging**: Every action is logged for compliance and security
- **Role-Based Access Control**: Flexible RBAC system with organization-level permissions

### API Design Principles
- **Custom MIME Types**: Following Houzer convention with versioned content types
- **Comprehensive Error Codes**: Structured error handling with specific error codes
- **Rate Limiting**: Built-in protection against abuse and brute force attacks
- **Security-First**: JWT tokens, password hashing, and session management

## 📋 API Endpoints

### 1. OTP Verification Endpoints

#### Send OTP
- **Path**: `POST /public/api/v1/verification/send-otp`
- **MIME Type**: `application/vnd.chidhagni-houzer.verification.send-otp.req-v1+json`
- **Purpose**: Sends OTP to email or mobile number
- **Rate Limit**: 3 requests per 15 minutes per IP

#### Verify OTP
- **Path**: `POST /public/api/v1/verification/verify-otp`
- **MIME Type**: `application/vnd.chidhagni-houzer.verification.verify-otp.req-v1+json`
- **Purpose**: Verifies the received OTP
- **Max Attempts**: 3 attempts per OTP

#### Resend OTP
- **Path**: `POST /public/api/v1/verification/resend-otp`
- **MIME Type**: `application/vnd.chidhagni-houzer.verification.resend-otp.req-v1+json`
- **Purpose**: Resends OTP with 30-second cooldown

### 2. Authentication Endpoints

#### Google Signup
- **Path**: `POST /public/api/v1/auth/google-signup`
- **Purpose**: Register using Google OAuth with organization creation
- **Database Impact**: Creates entries in Individual, Organization, and audit tables

#### Standard Signup
- **Path**: `POST /public/api/v1/signup`
- **MIME Type**: `application/vnd.chidhagni-houzer.signup.req-v1+json`
- **Purpose**: Register after email verification
- **Requires**: Valid verification token from OTP verification

#### Login
- **Path**: `POST /api/v1/login`
- **MIME Type**: `application/vnd.chidhagni-houzer.login.req-v1+json`
- **Purpose**: Authenticate and receive JWT tokens
- **Returns**: Access token, refresh token, user and organization data

#### Password Management
- **Forgot Password**: `POST /public/api/v1/forgot-password`
- **Reset Password**: `POST /public/api/v1/reset-password`

## 🗄️ Database Schema

### Core Tables

#### Individual Table
- Primary user entity with authentication data
- Supports both email/password and Google authentication
- Tracks login attempts and account security

#### Organization Table
- Multi-tenant organization support
- Various organization types (Private Limited, Partnership, etc.)
- Complete address and contact information

#### Individual Organization Mapping
- Many-to-many relationship between users and organizations
- Role-based access within organizations
- Primary organization designation

### Audit and Security Tables

#### Individual Verification Audit
- Complete OTP verification history
- Rate limiting and attempt tracking
- IP address and user agent logging

#### Individual Password Reset Audit
- Password reset request tracking
- Token management and expiration
- Security event logging

#### Individual Session
- JWT refresh token management
- Device-based session tracking
- Session revocation capabilities

### RBAC Tables

#### Roles and Permissions
- Flexible role-based access control
- Organization-specific and system-wide roles
- Granular permission management

## 🚀 Getting Started

### Prerequisites
```bash
Node.js 18+
PostgreSQL 14+
Redis (for production)
SendGrid or AWS SES (for emails)
Twilio (for SMS)
Google OAuth credentials
```

### Backend Setup

1. **Clone and Install**
```bash
git clone <repository-url>
cd houzer-auth-backend
npm install
```

2. **Environment Configuration**
```bash
cp .env.example .env
# Configure the following:
DATABASE_URL=postgresql://user:password@localhost:5432/houzer_auth
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-super-secure-jwt-secret
JWT_REFRESH_SECRET=your-super-secure-refresh-secret
SENDGRID_API_KEY=your-sendgrid-api-key
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

3. **Database Setup**
```bash
npx prisma migrate dev
npx prisma db seed
```

4. **Start Development Server**
```bash
npm run dev
```

### Frontend Setup

1. **Install Dependencies**
```bash
cd houzer-auth-frontend
npm install
```

2. **Environment Configuration**
```bash
REACT_APP_API_URL=http://localhost:8080
REACT_APP_GOOGLE_CLIENT_ID=your-google-client-id
REACT_APP_VERSION=1.0.0
```

3. **Start Development Server**
```bash
npm start
```

## 🔧 Configuration

### OTP Configuration
- **Length**: 6 digits
- **Expiry**: 5 minutes
- **Max Attempts**: 3 per OTP
- **Resend Cooldown**: 30 seconds

### Rate Limiting
- **OTP Requests**: 3 per 15 minutes per IP
- **Login Attempts**: 5 per 15 minutes per IP
- **Password Reset**: 3 per hour per email

### Security Settings
- **JWT Access Token**: 15 minutes expiry
- **JWT Refresh Token**: 7 days expiry
- **Account Lockout**: 15 minutes after 5 failed attempts
- **Password Requirements**: 8+ chars, mixed case, numbers, symbols

## 📚 Documentation

### Complete Documentation Set
1. **[Houzer API Specification](docs/houzer-api-specification.md)** - Complete API documentation
2. **[Implementation Guide](docs/houzer-implementation-guide.md)** - Backend implementation details
3. **[Frontend Integration](docs/houzer-frontend-integration.md)** - React.js integration guide
4. **[Database Design](docs/database-design.md)** - Original database design reference
5. **[API Contracts](docs/api-contracts.md)** - General API patterns reference

### Key Features Documentation

#### OTP Verification System
- Multi-channel support (Email, SMS)
- Rate limiting and security measures
- Comprehensive audit logging
- Automatic cleanup of expired OTPs

#### Google Integration
- OAuth 2.0 implementation
- Automatic user profile creation
- Organization linking
- Profile picture handling

#### Audit Logging
- Every verification attempt logged
- Password reset tracking
- Login attempt monitoring
- IP address and device tracking

## 🔒 Security Features

### Authentication Security
- **bcrypt Password Hashing**: 12 rounds with salt
- **JWT Token Management**: Short-lived access tokens with refresh mechanism
- **Account Lockout**: Automatic protection against brute force attacks
- **Session Management**: Device-based session tracking and revocation

### API Security
- **Rate Limiting**: Comprehensive rate limiting on all endpoints
- **Input Validation**: Strict validation and sanitization
- **CORS Protection**: Configurable CORS policies
- **Security Headers**: Helmet.js security headers
- **Custom MIME Types**: Versioned content types for API evolution

### Data Protection
- **Audit Trails**: Complete logging for compliance
- **PII Protection**: Masked contact information in responses
- **Token Security**: Secure token generation and storage
- **IP Tracking**: Geographic and security monitoring

## 🚀 Deployment

### Docker Deployment
```bash
# Build and run with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f backend
```

### Production Considerations
- SSL/TLS termination at load balancer
- Redis cluster for session management
- Database connection pooling
- Email service configuration (SendGrid/AWS SES)
- SMS service setup (Twilio)
- Google OAuth production credentials
- Monitoring and alerting setup

### Health Checks
- `GET /health` - Basic health check
- `GET /health/ready` - Readiness probe
- `GET /health/live` - Liveness probe

## 🧪 Testing

### Backend Testing
```bash
npm run test              # Unit tests
npm run test:integration  # Integration tests
npm run test:e2e         # End-to-end tests
npm run test:coverage    # Coverage report
```

### Frontend Testing
```bash
npm test                 # Jest tests
npm run test:coverage    # Coverage report
npm run test:e2e        # Cypress E2E tests
```

## 📊 Monitoring and Analytics

### Metrics to Monitor
- OTP delivery rates and verification success
- Login success/failure rates
- API response times and error rates
- Database connection pool usage
- Redis cache hit rates

### Logging Strategy
- Structured JSON logging
- Correlation IDs for request tracking
- Security event alerting
- Performance monitoring

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Follow the coding standards and add tests
4. Commit your changes (`git commit -m 'Add amazing feature'`)
5. Push to the branch (`git push origin feature/amazing-feature`)
6. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Houzer application team for the architecture patterns
- Material-UI for the excellent React components
- Prisma for the powerful ORM
- The open-source community for the amazing tools

---

For detailed implementation instructions, please refer to the comprehensive documentation in the `docs/` directory.
