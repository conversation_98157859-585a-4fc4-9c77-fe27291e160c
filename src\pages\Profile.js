import {
  Alert,
  Avatar,
  Box,
  Button,
  CircularProgress,
  Container,
  Paper,
  Snackbar,
  Typography
} from '@mui/material';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import authService from '../api/authService';

const Profile = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showError, setShowError] = useState(false);
  const [userData, setUserData] = useState(null);

  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    const handleSuccess = (data) => {
      console.log('Profile fetched successfully:', data);
      setUserData(data);
    };

    const handleError = (errorMessage) => {
      setError(errorMessage);
      setShowError(true);
    };

    try {
      await authService.getProfile(handleError, handleSuccess);
    } catch (err) {
      // Error already handled by authService
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    authService.logout();
    navigate('/login');
  };

  if (loading) {
    return (
      <Container sx={{ display: 'flex', justifyContent: 'center', mt: 8 }}>
        <CircularProgress />
      </Container>
    );
  }

  return (
    <Container component="main" maxWidth="sm">
      <Paper elevation={3} sx={{ p: 4, mt: 8 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <Avatar
            sx={{
              width: 100,
              height: 100,
              mb: 2,
              bgcolor: 'primary.main'
            }}
          >
            {userData?.name?.charAt(0)?.toUpperCase() || 'U'}
          </Avatar>
          <Typography component="h1" variant="h5" gutterBottom>
            Profile
          </Typography>
          {userData && (
            <Box sx={{ width: '100%', mt: 2 }}>
              <Typography variant="body1" gutterBottom>
                <strong>Name:</strong> {userData.name}
              </Typography>
              <Typography variant="body1" gutterBottom>
                <strong>Email:</strong> {userData.email}
              </Typography>
              {/* Add more user data fields as needed */}
            </Box>
          )}
          <Button
            variant="contained"
            color="secondary"
            onClick={handleLogout}
            sx={{ mt: 3 }}
          >
            Logout
          </Button>
        </Box>
      </Paper>
      <Snackbar
        open={showError}
        autoHideDuration={6000}
        onClose={() => setShowError(false)}
      >
        <Alert severity="error" onClose={() => setShowError(false)}>
          {error}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default Profile; 