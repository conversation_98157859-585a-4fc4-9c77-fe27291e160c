import { GOOGLE_OAUTH_CONFIG } from '../config/googleOAuth';

class GoogleAuthService {
  constructor() {
    this.isInitialized = true; // Always ready since we're using URL redirect
  }

  // Initialize - no longer needed but kept for compatibility
  async initializeGapi() {
    return Promise.resolve(true);
  }

  // Sign in with Google using URL redirect
  async signIn() {
    try {
      // Redirect to <PERSON>uzer's Google OAuth URL
      window.location.href = GOOGLE_OAUTH_CONFIG.authUrl;

      // This will never execute as we're redirecting
      return {
        success: true,
        redirecting: true,
      };
    } catch (error) {
      console.error('Google sign-in error:', error);
      return {
        success: false,
        error: error.message || 'Google sign-in failed',
      };
    }
  }

  // Sign out - simplified since we're using URL-based auth
  async signOut() {
    try {
      // Clear any stored tokens
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      return { success: true };
    } catch (error) {
      console.error('Google sign-out error:', error);
      return { success: false, error: 'Sign-out failed' };
    }
  }

  // Check if user is signed in based on stored token
  isSignedIn() {
    return !!localStorage.getItem('token');
  }

  // Get current user from localStorage
  getCurrentUser() {
    try {
      const userStr = localStorage.getItem('user');
      return userStr ? JSON.parse(userStr) : null;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  // Handle OAuth redirect callback (if needed)
  handleOAuthCallback(urlParams) {
    try {
      // Extract parameters from URL if needed
      const code = urlParams.get('code');
      const state = urlParams.get('state');

      if (code) {
        return {
          success: true,
          code,
          state,
        };
      }

      return {
        success: false,
        error: 'No authorization code received',
      };
    } catch (error) {
      console.error('OAuth callback error:', error);
      return {
        success: false,
        error: 'Failed to handle OAuth callback',
      };
    }
  }
}

// Create and export a singleton instance
const googleAuthService = new GoogleAuthService();
export default googleAuthService;
