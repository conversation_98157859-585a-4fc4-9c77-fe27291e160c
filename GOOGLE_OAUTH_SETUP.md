# Google OAuth Integration Setup Guide

This guide will walk you through setting up Google OAuth authentication for your React application.

## Prerequisites

- Google Cloud Console account
- React application with Material-UI

## Step 1: Google Cloud Console Setup

### 1.1 Create a Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click on the project dropdown at the top
3. Click "New Project"
4. Enter a project name and click "Create"

### 1.2 Enable Required APIs

1. In the Google Cloud Console, go to "APIs & Services" > "Library"
2. Search for and enable the following APIs:
   - **Google+ API** (for user profile information)
   - **Google OAuth2 API** (for authentication)

### 1.3 Create OAuth 2.0 Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client ID"
3. If prompted, configure the OAuth consent screen:
   - Choose "External" user type
   - Fill in the required fields (App name, User support email, Developer contact)
   - Add your domain to authorized domains
4. Select "Web application" as the application type
5. Add authorized JavaScript origins:
   - `http://localhost:3000` (for development)
   - Your production domain (e.g., `https://yourdomain.com`)
6. Add authorized redirect URIs:
   - `http://localhost:3000` (for development)
   - Your production domain (e.g., `https://yourdomain.com`)
7. Click "Create"
8. Copy the Client ID (you'll need this for your React app)

## Step 2: React Application Configuration

### 2.1 Environment Variables

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` and replace the placeholder with your actual Google Client ID:
   ```
   REACT_APP_GOOGLE_CLIENT_ID=your_actual_client_id_here
   ```

### 2.2 Install Dependencies

The required dependencies are already included in package.json:
- `gapi-script`: For Google API integration

## Step 3: Backend Integration

Your backend needs to handle Google OAuth tokens. Here's what your backend should implement:

### 3.1 Google OAuth Endpoint

Create an endpoint at `POST /api/v1/auth/google` that:

1. Receives the Google ID token from the frontend
2. Verifies the token with Google's servers
3. Extracts user information (email, name, etc.)
4. Creates or updates the user in your database
5. Returns your application's JWT token

### Example Backend Implementation (Node.js/Express):

```javascript
const { OAuth2Client } = require('google-auth-library');
const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

app.post('/api/v1/auth/google', async (req, res) => {
  try {
    const { token } = req.body;
    
    // Verify the Google token
    const ticket = await client.verifyIdToken({
      idToken: token,
      audience: process.env.GOOGLE_CLIENT_ID,
    });
    
    const payload = ticket.getPayload();
    const { email, name, picture } = payload;
    
    // Find or create user in your database
    let user = await User.findOne({ email });
    if (!user) {
      user = await User.create({
        email,
        name,
        profilePicture: picture,
        authProvider: 'google'
      });
    }
    
    // Generate your app's JWT token
    const appToken = generateJWT(user);
    
    res.json({
      token: appToken,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        profilePicture: user.profilePicture
      }
    });
  } catch (error) {
    res.status(401).json({ message: 'Invalid Google token' });
  }
});
```

## Step 4: Testing

1. Start your React development server:
   ```bash
   npm start
   ```

2. Navigate to the login or signup page
3. Click "Continue with Google"
4. Complete the Google OAuth flow
5. Verify that you're redirected back to your application

## Troubleshooting

### Common Issues:

1. **"Invalid Client ID"**: Make sure your Client ID is correctly set in the `.env` file
2. **"Unauthorized JavaScript origin"**: Add your domain to authorized origins in Google Cloud Console
3. **"Redirect URI mismatch"**: Ensure redirect URIs match exactly in Google Cloud Console
4. **CORS errors**: Make sure your backend allows requests from your frontend domain

### Development vs Production:

- **Development**: Use `http://localhost:3000`
- **Production**: Use your actual domain with HTTPS (e.g., `https://yourdomain.com`)

## Security Considerations

1. Never expose your Google Client Secret in frontend code
2. Always verify Google tokens on your backend
3. Use HTTPS in production
4. Implement proper session management
5. Consider implementing refresh token rotation

## Features Included

- ✅ Responsive design that works on all devices
- ✅ Sleek, modern UI with Material-UI components
- ✅ Google OAuth integration with proper error handling
- ✅ Loading states and user feedback
- ✅ Seamless integration with existing authentication flow
- ✅ Proper token management and storage
- ✅ Mobile-optimized design with touch-friendly buttons

## Support

If you encounter any issues, please check:
1. Google Cloud Console configuration
2. Environment variables
3. Backend implementation
4. Network connectivity
5. Browser console for error messages
