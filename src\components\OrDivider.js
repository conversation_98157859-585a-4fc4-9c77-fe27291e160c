import { Box, Divider, Typography } from '@mui/material';

const OrDivider = ({ text = "OR", sx = {} }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        my: 2,
        ...sx
      }}
    >
      <Divider sx={{ flex: 1 }} />
      <Typography
        variant="body2"
        sx={{
          px: 2,
          color: 'text.secondary',
          fontSize: '12px',
          fontWeight: 500,
          textTransform: 'uppercase',
          letterSpacing: '0.5px'
        }}
      >
        {text}
      </Typography>
      <Divider sx={{ flex: 1 }} />
    </Box>
  );
};

export default OrDivider;
